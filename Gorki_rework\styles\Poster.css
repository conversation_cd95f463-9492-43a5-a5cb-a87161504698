/* Poster Page */
.poster-page {
  position: relative;
  width: 100%;
  min-height: 100vh;
  padding: 40px 0 40px;
  background: #ffffff;
  display: grid;
  grid-template-columns: repeat(2, 490px);
  grid-template-rows: auto auto auto;
  gap: 60px 156px;
  justify-content: center;
  align-content: start;
}

/* Заголовок афиши */
.afisha-title {
  grid-column: 1 / -1;
  font-family: 'G<PERSON>bera-Regular', Arial, sans-serif;
  font-style: normal;
  font-weight: 500;
  font-size: 48px;
  line-height: 60px;
  text-align: center;
  color: #000000;
  margin: 0 0 40px 0;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

/* Общие стили для всех карточек */
.group-13,
.group-12,
.group-14,
.group-15 {
  position: relative;
  width: 490px;
  height: 644px;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
}

.group-13:hover,
.group-12:hover,
.group-14:hover,
.group-15:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.25);
}

/* Скрываем rectangle элементы */
.rectangle-12,
.rectangle-8,
.rectangle-15,
.rectangle-14 {
  display: none;
}

/* Изображения карточек */
.group-13 .image,
.group-12 .image,
.group-14 .image,
.group-15 .image {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background-size: cover;
  background-position: center;
  border-radius: 20px;
  transition: transform 0.3s ease;
}

.group-13:hover .image,
.group-12:hover .image,
.group-14:hover .image,
.group-15:hover .image {
  transform: scale(1.03);
}

/* Кнопки */
.group-13 .button,
.group-12 .button,
.group-14 .button,
.group-15 .button {
  position: absolute;
  bottom: 20px;
  right: 20px;
  width: 141px;
  height: 56px;
  background: #FCD619;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  z-index: 10;
  backdrop-filter: blur(5px);
  cursor: pointer;
}

.group-13 .button:hover,
.group-12 .button:hover,
.group-14 .button:hover,
.group-15 .button:hover {
  transform: scale(1.05);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

/* Текст кнопок */
.group-13 .button-text,
.group-12 .button-text,
.group-14 .button-text,
.group-15 .button-text {
  position: relative;
  font-family: 'Gerbera-Regular', Arial, sans-serif;
  font-style: normal;
  font-weight: 500;
  font-size: 15px;
  line-height: 18px;
  text-align: center;
  color: #000000;
}


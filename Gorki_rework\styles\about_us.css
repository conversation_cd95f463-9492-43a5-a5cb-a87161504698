body {
    font-family: 'G<PERSON>bera-Regular', Aria<PERSON>, sans-serif;
    background: #ffffff;
    padding: 0;
    margin: 0;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* О выставке */
.about-us-title {
    width: 100%;
    height: 53px;
    margin-top: 50px;
    padding-top: 20px;
    font-family: 'G<PERSON>bera-Regular', Aria<PERSON>, sans-serif;
    font-weight: 500;
    font-size: 40px;
    line-height: 53px;
    text-align: center;
    color: #000000;
}

/* Главная секция */
.main-section {
    display: flex;
    align-items: flex-start;
    gap: 60px;
    max-width: 1200px;
    margin: 40px auto 0;
    padding: 0 80px;
}

.main-image-container {
    flex: 0 0 600px;
}

.main-image {
    width: 600px;
    height: 450px;
    border-radius: 20px;
    object-fit: cover;
}

.main-info {
    flex: 1;
    padding-top: 20px;
}

.main-ticket-btn {
    width: 300px;
    height: 70px;
    background: #FCD619;
    border-radius: 35px;
    border: none;
    font-family: 'Gerbera-Regular', Arial, sans-serif;
    font-size: 20px;
    font-weight: 500;
    color: #000000;
    cursor: pointer;
    margin-bottom: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    transition: all 0.3s ease;
}

.main-ticket-btn:hover {
    background: #E5C517;
    transform: scale(1.05);
}

.main-text {
    font-family: 'Gerbera-Regular', Arial, sans-serif;
    font-size: 18px;
    line-height: 26px;
    color: #000000;
    max-width: 400px;
}

/* Блоки контента */
.content-blocks {
    width: 100%;
    max-width: 1200px;
    margin: 80px auto 0;
    padding: 0 80px;
}

.content-block {
    display: flex;
    align-items: flex-start;
    gap: 80px;
    margin-bottom: 100px;
}

.block-image {
    flex: 0 0 400px;
}

.block-img {
    width: 400px;
    height: 500px;
    border-radius: 20px;
    object-fit: cover;
}


.dark-image .block-img {
    width: 360px;
    height: 460px;
}

.block-text {
    flex: 1;
    font-family: 'Gerbera-Regular', Arial, sans-serif;
    font-size: 18px;
    line-height: 26px;
    color: #000000;
    padding-top: 40px;
    max-width: 600px;
}

/* Блок 2 - обычный порядок (текст слева, изображение справа) */
.block-2 {
    flex-direction: row;
}

/* Текст первого блока - позиция будет задана в HTML */
.about-us-museum-text {
    position: absolute;
    width: 342px;
    height: 312px;
    left: 915px;
    top: 420px;
    font-family: 'Gerbera-Regular', Arial, sans-serif;
    font-style: normal;
    font-weight: 400;
    font-size: 18px;
    line-height: 24px;
    color: #000000;
}

/* Group 9 - Второй блок */
.about-us-decorative-block {
    position: absolute;
    width: 917px;
    height: 570px;
    left: 261px;
    top: 917px;
}

/* Усадьба 1 1 */
.about-us-decorative-image {
    position: absolute;
    width: 380px;
    height: 570px;
    left: 261px;
    top: 917px;
    background: url('Усадьба 1.png') center/cover no-repeat;
    border-radius: 20px;
}

/* Усадьба Горки – главный объект... */
.about-us-decorative-text {
    position: absolute;
    width: 342px;
    height: 312px;
    left: 836px;
    top: 1046px;
    font-family: 'Gerbera-Regular', Arial, sans-serif;
    font-style: normal;
    font-weight: 400;
    font-size: 18px;
    line-height: 24px;
    color: #000000;
}

/* Group 8 - Третий блок */
.about-us-architecture-block {
    position: absolute;
    width: 917px;
    height: 570px;
    left: 261px;
    top: 1509px;
}

/* Формирование архитектурно-паркового ансамбля... */
.about-us-architecture-text {
    position: absolute;
    width: 342px;
    height: 312px;
    left: 261px;
    top: 1638px;
    font-family: 'Gerbera-Regular', Arial, sans-serif;
    font-style: normal;
    font-weight: 400;
    font-size: 18px;
    line-height: 24px;
    color: #000000;
}

/* Усадьба 2 1 */
.about-us-architecture-image {
    position: absolute;
    width: 380px;
    height: 570px;
    left: 798px;
    top: 1509px;
    background: url('Усадьба  2.png') center/cover no-repeat;
    border-radius: 20px;
}

/* Group 10 - Четвертый блок */
.about-us-interior-block {
    position: absolute;
    width: 917px;
    height: 570px;
    left: 261px;
    top: 2101px;
}

/* Усадьба 4 1 */
.about-us-interior-image {
    position: absolute;
    width: 380px;
    height: 570px;
    left: 261px;
    top: 2101px;
    background: url('Усадьба 4.png') center/cover no-repeat;
    border-radius: 20px;
}

/* В 1909 г. имение приобрела... */
.about-us-interior-text {
    position: absolute;
    width: 342px;
    height: 312px;
    left: 836px;
    top: 2230px;
    font-family: 'Gerbera-Regular', Arial, sans-serif;
    font-style: normal;
    font-weight: 400;
    font-size: 18px;
    line-height: 24px;
    color: #000000;
}


/* Map Section */
.map-section {
  position: relative;
  width: 1440px;
  margin: 60px auto 0;
  padding-top: 0;
}

/* Section Title */
.map-title {
  position: relative;
  width: auto;
  margin: 0 0 24px 187px;
  font-family: 'Gerbera-Regular', Arial, sans-serif;
  font-style: normal;
  font-weight: 400;
  font-size: 40px;
  line-height: 53px;
  color: #000000;
}

/* Map Container */
.map-container {
  position: relative;
  width: 1109px;
  height: 304px;
  margin: 0 auto;
  background: #9C8F80;
  border-radius: 20px;
  overflow: hidden;
  display: flex;
}

/* Info Block - Contact Info */
.map-contact-info {
  position: absolute;
  width: 265px;
  left: 45px;
  top: 22px;
  font-family: 'Gerbera-Regular', Arial, sans-serif;
  font-style: normal;
  font-weight: 400;
  font-size: 20px;
  line-height: 27px;
  color: #FFFFFF;
  z-index: 2;
}

/* Info Block - Excursions */
.map-excursions-info {
  position: absolute;
  width: 262px;
  left: 330px;
  top: 22px;
  font-family: 'Gerbera-Regular', Arial, sans-serif;
  font-style: normal;
  font-weight: 400;
  font-size: 20px;
  line-height: 27px;
  color: #FFFFFF;
  z-index: 2;
}


/* Info Block - Address */
.map-address {
  position: absolute;
  width: 541px;
  left: 51px;
  bottom: 25px;
  font-family: 'Gerbera-Regular', Arial, sans-serif;
  font-style: normal;
  font-weight: 400;
  font-size: 20px;
  line-height: 27px;
  color: #FFFFFF;
  z-index: 2;
}

/* Map Image */
.map-image {
  position: absolute;
  width: 479px;
  height: 304px;
  right: 0;
  top: 0;
  object-fit: cover;
  border-radius: 0 20px 20px 0;
}

/* Responsive text formatting */
.map-contact-info p,
.map-excursions-info p,
.map-address p {
  margin: 0;
  padding: 0;
}

.map-contact-info strong,
.map-excursions-info strong {
  display: block;
  margin-bottom: 5px;
}

/* Footer */
.footer {
    width: 100%;
    background: #9C8F80;
    margin-top: 100px;
    padding: 60px 0;
}

.footer-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 80px;
    display: flex;
    justify-content: space-between;
}

.footer-column {
    flex: 1;
    margin-right: 60px;
}

.footer-column:last-child {
    margin-right: 0;
}

.footer-column h4 {
    font-family: 'Gerbera-Regular', Arial, sans-serif;
    font-size: 20px;
    font-weight: 600;
    color: #FFFFFF;
    margin-bottom: 20px;
}

.footer-column ul {
    list-style: none;
    padding: 0;
}

.footer-column li {
    font-family: 'Gerbera-Regular', Arial, sans-serif;
    font-size: 16px;
    line-height: 24px;
    color: #FFFFFF;
    margin-bottom: 8px;
    text-decoration: underline;
    cursor: pointer;
}

/* Общие стили для текста */
.about-us-text-content {
    font-family: 'Gerbera-Regular', Arial, sans-serif;
    font-style: normal;
    font-weight: 400;
    font-size: 18px;
    line-height: 24px;
    color: #000000;
    margin: 0;
    padding: 0;
}

/* Убираем стандартные отступы */
.about-us-contact-block,
.about-us-excursion-block,
.about-us-address-block {
    margin: 0;
    padding: 0;
}

.about-us-contact-block h3,
.about-us-excursion-block h3 {
    display: none;
}

.about-us-contact-block p,
.about-us-excursion-block p,
.about-us-address-block p {
    margin: 0;
    padding: 0;
}
/* Hero Section */
.hero-section {
  position: relative;
  width: 1132px;
  height: 326px;
  margin: 30px auto 0;
}

/* Rectangle 1 (Background) */
.hero-background {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  background: url('../static/images/hero/Page 1.jpg') no-repeat center;
  background-size: contain;
  border-radius: 40px;
  transition: opacity 0.5s ease-in-out;
}

/* Hero Indicators */
.hero-indicators {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 10px;
  z-index: 10;
}

.hero-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.5);
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.hero-indicator:hover {
  background: rgba(255, 255, 255, 0.8);
  transform: scale(1.2);
}

.hero-indicator.active {
  background: #FCD619;
  border-color: #FFFFFF;
  transform: scale(1.3);
}

/* Button 1 (Left) */
.hero-button-left {
  position: absolute;
  width: 45px;
  height: 45px;
  left: 10px;
  top: 50%;
  transform: translateY(-50%);
  background: #FCD619;
  border-radius: 50%;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

/* Chevron Icon Left */
.hero-button-left .chevron-left {
  width: 30px;
  height: 30px;
  display: block;
  background: url('../static/images/icon/chevron-left-icon.png') no-repeat center;
  background-size: contain;
  font-size: 0;
}

/* Button 2 (Right) */
.hero-button-right {
  position: absolute;
  width: 45px;
  height: 45px;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  background: #FCD619;
  border-radius: 50%;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

/* Chevron Icon Right */
.hero-button-right .chevron-right {
  width: 30px;
  height: 30px;
  display: block;
  background: url('../static/images/icon/chevron-right-icon.png') no-repeat center;
  background-size: contain;
  font-size: 0;
}



/* Museum Section */
.museum-section {
  position: relative;
  width: 1440px;
  margin: 60px auto 0;
  padding-top: 0;
}

/* Section Title */
.museum-title {
  position: relative;
  width: 126px;
  height: 53px;
  margin: 0 auto 37px;
  font-family: 'Gerbera-Regular', Arial, sans-serif;
  font-style: normal;
  font-weight: 400;
  font-size: 40px;
  line-height: 53px;
  color: #000000;
  text-align: center;
}

/* Museum Cards Container */
.museum-cards {
  display: flex;
  gap: 115px;
  position: relative;
  justify-content: center;
  margin-bottom: 23px;
}

/* Museum Card */
.museum-card {
  position: relative;
  width: 279px;
  height: 352px;
  background: #EDEDED;
  border-radius: 20px;
  overflow: hidden;
}

/* Card Image */
.museum-card-image {
  width: 279px;
  height: 186px;
  object-fit: cover;
  border-radius: 20px 20px 0 0;
}

/* Card Title */
.museum-card-title {
  position: absolute;
  width: 258px;
  left: 10px;
  top: 190px;
  font-family: 'Gerbera-Regular', Arial, sans-serif;
  font-style: normal;
  font-weight: 400;
  font-size: 15px;
  line-height: 18px;
  color: #000000;
}

/* Card Description */
.museum-card-description {
  position: absolute;
  width: 258px;
  height: 76px;
  left: 10px;
  top: 220px;
  font-family: 'Gerbera-Regular', Arial, sans-serif;
  font-style: normal;
  font-weight: 400;
  font-size: 12px;
  line-height: 15px;
  color: #000000;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Card Buttons Container */
.museum-card-buttons {
  position: absolute;
  bottom: 8px;
  left: 70px;
  display: flex;
  gap: 15px;
}

/* Card Button */
.museum-card-button {
  width: 93px;
  height: 37px;
  background: #FCD619;
  border-radius: 20px;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
  font-family: 'Gerbera-Regular', Arial, sans-serif;
  font-style: normal;
  font-weight: 400;
  font-size: 11px;
  line-height: 13px;
  color: #000000;
  transition: background 0.3s ease;
}

.museum-card-button:hover {
  background: #FCD619;
}

/* Navigation */
.museum-navigation {
  position: relative;
  width: 210px;
  height: 45px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* Navigation Button */
.museum-nav-button {
  width: 45px;
  height: 45px;
  background: #FCD619;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.3s ease;
}

.museum-nav-button:hover {
  background: #FCD619;
}

/* Navigation Chevron Icons */
.museum-nav-button.prev::after {
  content: '';
  display: block;
  width: 20px;
  height: 20px;
  background: url('../static/images/icon/museum-nav-chevron-left-icon.png') no-repeat center;
  background-size: contain;
}

.museum-nav-button.next::after {
  content: '';
  display: block;
  width: 20px;
  height: 20px;
  background: url('../static/images/icon/museum-nav-chevron-right-icon.png') no-repeat center;
  background-size: contain;
}

/* Hide SVG if present */
.museum-nav-button svg {
  display: none;
}

/* Navigation Counter */
.museum-nav-counter {
  font-family: 'Gerbera-Regular', Arial, sans-serif;
  font-style: normal;
  font-weight: 400;
  font-size: 18px;
  line-height: 22px;
  color: #000000;
}


/* Virtual Exhibitions Section */
.virtual-exhibitions {
  position: relative;
  width: 1440px;
  margin: 60px auto 0;
  padding-top: 0;
}

/* Section Title */
.virtual-exhibitions-title {
  position: relative;
  width: auto;
  margin: 0 auto 37px;
  font-family: 'Gerbera-Regular', Arial, sans-serif;
  font-style: normal;
  font-weight: 400;
  font-size: 40px;
  line-height: 53px;
  color: #000000;
  text-align: center;
}

/* Virtual Exhibition Cards Container */
.virtual-exhibitions-cards {
  display: flex;
  gap: 106px;
  position: relative;
  justify-content: center;
  margin-bottom: 23px;
}

/* Virtual Exhibition Card */
.virtual-exhibition-card {
  position: relative;
  width: 279px;
  height: 352px;
  background: #E3E3E3;
  border-radius: 20px;
  overflow: hidden;
}

/* Card Image - Full card size */
.virtual-exhibition-card-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 20px;
}

/* Card Title */
.virtual-exhibition-card-title {
  position: absolute;
  width: 258px;
  left: 10px;
  top: 190px;
  font-family: 'Gerbera-Regular', Arial, sans-serif;
  font-style: normal;
  font-weight: 400;
  font-size: 15px;
  line-height: 18px;
  color: #000000;
}

/* Card Description */
.virtual-exhibition-card-description {
  position: absolute;
  width: 258px;
  height: 76px;
  left: 10px;
  top: 220px;
  font-family: 'Gerbera-Regular', Arial, sans-serif;
  font-style: normal;
  font-weight: 400;
  font-size: 12px;
  line-height: 15px;
  color: #000000;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Card Button */
.virtual-exhibition-card-button {
  position: absolute;
  width: 93px;
  height: 37px;
  right: 8px;
  bottom: 8px;
  background: #FCD619;
  border-radius: 20px;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
  font-family: 'Gerbera-Regular', Arial, sans-serif;
  font-style: normal;
  font-weight: 400;
  font-size: 11px;
  line-height: 13px;
  text-align: center;
  color: #000000;
  transition: background 0.3s ease;
}

.virtual-exhibition-card-button:hover {
  background: #8F8F8F;
}

/* Navigation */
.virtual-exhibitions-navigation {
  position: relative;
  width: 210px;
  height: 45px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* Navigation Button */
.virtual-exhibitions-nav-button {
  width: 45px;
  height: 45px;
  background: #FCD619;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.3s ease;
}

.virtual-exhibitions-nav-button:hover {
  background: #FCD619;
}

/* Navigation Chevron Icons */
.virtual-exhibitions-nav-button.prev::after {
  content: '';
  display: block;
  width: 20px;
  height: 20px;
  background: url('../static/images/icon/virtual-nav-chevron-left-icon.png') no-repeat center;
  background-size: contain;
}

.virtual-exhibitions-nav-button.next::after {
  content: '';
  display: block;
  width: 20px;
  height: 20px;
  background: url('../static/images/icon/virtual-nav-chevron-right-icon.png') no-repeat center;
  background-size: contain;
}

/* Hide SVG if present */
.virtual-exhibitions-nav-button svg {
  display: none;
}

/* Navigation Counter */
.virtual-exhibitions-nav-counter {
  font-family: 'Gerbera-Regular', Arial, sans-serif;
  font-style: normal;
  font-weight: 400;
  font-size: 18px;
  line-height: 22px;
  color: #000000;
}


/* Poster Section */
.poster-section {
  position: relative;
  width: 1440px;
  margin: 60px auto 0;
  padding-top: 0;
}

/* Poster Section Title */
.poster-section .poster-title {
  position: relative;
  width: auto;
  margin: 0 auto 37px;
  font-family: 'Gerbera-Regular', Arial, sans-serif;
  font-style: normal;
  font-weight: 400;
  font-size: 40px;
  line-height: 53px;
  color: #000000;
  text-align: center;
}

/* Poster Cards Container */
.poster-section .poster-cards {
  display: flex;
  gap: 106px;
  position: relative;
  justify-content: center;
  margin-bottom: 23px;
}

/* Poster Card */
.poster-section .poster-card {
  position: relative;
  width: 279px;
  height: 352px;
  background: #E3E3E3;
  border-radius: 20px;
  overflow: hidden;
}

/* Poster Card Image - Full card size */
.poster-section .poster-card-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 20px;
}

/* Poster Card Title */
.poster-section .poster-card-title {
  position: absolute;
  width: 258px;
  left: 10px;
  top: 190px;
  font-family: 'Gerbera-Regular', Arial, sans-serif;
  font-style: normal;
  font-weight: 400;
  font-size: 15px;
  line-height: 18px;
  color: #000000;
}

/* Poster Card Description */
.poster-section .poster-card-description {
  position: absolute;
  width: 258px;
  height: 76px;
  left: 10px;
  top: 220px;
  font-family: 'Gerbera-Regular', Arial, sans-serif;
  font-style: normal;
  font-weight: 400;
  font-size: 12px;
  line-height: 15px;
  color: #000000;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Poster Card Button */
.poster-section .poster-card-button {
  position: absolute;
  width: 93px;
  height: 37px;
  right: 8px;
  bottom: 8px;
  background: #FCD619;
  border-radius: 20px;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
  font-family: 'Gerbera-Regular', Arial, sans-serif;
  font-style: normal;
  font-weight: 400;
  font-size: 11px;
  line-height: 13px;
  text-align: center;
  color: #000000;
  transition: background 0.3s ease;
}

.poster-section .poster-card-button:hover {
  background: #FCD619;
}

/* Poster Navigation */
.poster-section .poster-navigation {
  position: relative;
  width: 210px;
  height: 45px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* Poster Navigation Button */
.poster-section .poster-nav-button {
  width: 45px;
  height: 45px;
  background: #FCD619;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.3s ease;
}

.poster-section .poster-nav-button:hover {
  background: #FCD619;
}

/* Poster Navigation Chevron Icons */
.poster-section .poster-nav-button.prev::after {
  content: '';
  display: block;
  width: 20px;
  height: 20px;
  background: url('../static/images/icon/virtual-nav-chevron-left-icon.png') no-repeat center;
  background-size: contain;
}

.poster-section .poster-nav-button.next::after {
  content: '';
  display: block;
  width: 20px;
  height: 20px;
  background: url('../static/images/icon/virtual-nav-chevron-right-icon.png') no-repeat center;
  background-size: contain;
}

/* Hide SVG if present */
.poster-section .poster-nav-button svg {
  display: none;
}

/* Poster Navigation Counter */
.poster-section .poster-nav-counter {
  font-family: 'Gerbera-Regular', Arial, sans-serif;
  font-style: normal;
  font-weight: 400;
  font-size: 18px;
  line-height: 22px;
  color: #000000;
}


/* Map Section */
.map-section {
  position: relative;
  width: 1440px;
  margin: 60px auto 0;
  padding-top: 0;
}

/* Section Title */
.map-title {
  position: relative;
  width: auto;
  margin: 0 0 24px 187px;
  font-family: 'Gerbera-Regular', Arial, sans-serif;
  font-style: normal;
  font-weight: 400;
  font-size: 40px;
  line-height: 53px;
  color: #000000;
}

/* Map Container */
.map-container {
  position: relative;
  width: 1109px;
  height: 304px;
  margin: 0 auto;
  background: #9C8F80;
  border-radius: 20px;
  overflow: hidden;
  display: flex;
}

/* Info Block - Contact Info */
.map-contact-info {
  position: absolute;
  width: 265px;
  left: 45px;
  top: 22px;
  font-family: 'Gerbera-Regular', Arial, sans-serif;
  font-style: normal;
  font-weight: 400;
  font-size: 20px;
  line-height: 27px;
  color: #FFFFFF;
  z-index: 2;
}

/* Info Block - Excursions */
.map-excursions-info {
  position: absolute;
  width: 262px;
  left: 330px;
  top: 22px;
  font-family: 'Gerbera-Regular', Arial, sans-serif;
  font-style: normal;
  font-weight: 400;
  font-size: 20px;
  line-height: 27px;
  color: #FFFFFF;
  z-index: 2;
}


/* Info Block - Address */
.map-address {
  position: absolute;
  width: 541px;
  left: 51px;
  bottom: 25px;
  font-family: 'Gerbera-Regular', Arial, sans-serif;
  font-style: normal;
  font-weight: 400;
  font-size: 20px;
  line-height: 27px;
  color: #FFFFFF;
  z-index: 2;
}

/* Map Image */
.map-image {
  position: absolute;
  width: 479px;
  height: 304px;
  right: 0;
  top: 0;
  object-fit: cover;
  border-radius: 0 20px 20px 0;
}

/* Responsive text formatting */
.map-contact-info p,
.map-excursions-info p,
.map-address p {
  margin: 0;
  padding: 0;
}

.map-contact-info strong,
.map-excursions-info strong {
  display: block;
  margin-bottom: 5px;
}



/* Footer */
.footer {
  position: relative;
  width: 1440px;
  margin: 80px auto 0;
  background: linear-gradient(135deg, #9C8F80 0%, #8A7D6F 100%);
  padding: 60px 0 40px;
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
}

/* Footer Content Container */
.footer-content {
  position: relative;
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 40px;
}

/* Footer Columns */
.footer-columns {
  display: flex;
  gap: 100px;
  justify-content: space-between;
  margin-bottom: 40px;
}

/* Footer Column */
.footer-column {
  display: flex;
  flex-direction: column;
  gap: 20px;
  flex: 1;
}

/* Footer Title */
.footer-title {
  font-family: 'Gerbera-Regular', Arial, sans-serif;
  font-style: normal;
  font-weight: 500;
  font-size: 18px;
  line-height: 24px;
  color: #FFFFFF;
  margin: 0 0 12px 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Footer Links List */
.footer-links {
  display: flex;
  flex-direction: column;
  gap: 14px;
  list-style: none;
  padding: 0;
  margin: 0;
}

/* Footer Link */
.footer-link {
  font-family: 'Gerbera-Regular', Arial, sans-serif;
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  color: rgba(255, 255, 255, 0.9);
  text-decoration: none;
  transition: all 0.3s ease;
  position: relative;
  display: inline-block;
}

.footer-link:hover {
  color: #FCD619;
  transform: translateX(5px);
}

.footer-link::before {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 2px;
  background: #FCD619;
  transition: width 0.3s ease;
}

.footer-link:hover::before {
  width: 100%;
}

/* Footer Separator Lines - Hidden by default, can be shown if needed */
.footer-separator {
  display: none;
}

/* Footer Contact Info */
.footer-contact {
  font-family: 'Gerbera-Regular', Arial, sans-serif;
  font-style: normal;
  font-weight: 400;
  font-size: 16px;
  line-height: 24px;
  color: #FFFFFF;
}

.footer-contact p {
  margin: 8px 0;
}

.footer-contact a {
  color: rgba(255, 255, 255, 0.9);
  text-decoration: none;
  transition: all 0.3s ease;
  display: inline-block;
  position: relative;
}

.footer-contact a:hover {
  color: #FCD619;
  transform: translateX(3px);
}

/* Footer Bottom Section */
.footer-bottom {
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  padding-top: 30px;
  margin-top: 40px;
  text-align: center;
}

.footer-bottom p {
  font-family: 'Gerbera-Regular', Arial, sans-serif;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
  margin: 0;
}

/* Responsive adjustments */
@media screen and (max-width: 1440px) {
  .footer {
    width: 100%;
  }

  .footer-content {
    padding: 0 20px;
  }

  .footer-columns {
    gap: 60px;
  }
}

@media screen and (max-width: 768px) {
  .footer-columns {
    flex-direction: column;
    gap: 40px;
  }

  .footer-column {
    width: 100%;
  }
}
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Gerbera-Regular', Arial, sans-serif;
    background: #ffffff;
    padding: 0;
    margin: 0;
}

/* Order Page Container */
.order-page-container {
    max-width: 1440px;
    margin: 0 auto;
    padding: 40px 40px 60px;
    background: #ffffff;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 40px 20px;
    background: white;
}

  .header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 15px 0 30px 0;
      border-bottom: 1px solid #e0e0e0;
      margin-bottom: 40px;
  }

  .logo {
      display: flex;
      align-items: center;
      gap: 10px;
  }

  .logo-icon {
      width: 40px;
      height: 40px;
      background: #ffd700;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: bold;
      color: #333;
  }

  .logo-text {
      font-family: 'Gerbera-Regular', <PERSON>l, sans-serif;
      font-size: 18px;
      font-weight: 600;
      color: #333;
  }

  .header-controls {
      display: flex;
      align-items: center;
      gap: 30px;
  }

  .buy-ticket-link {
      font-family: 'Gerbera-Regular', Arial, sans-serif;
      color: #333;
      text-decoration: none;
      font-size: 14px;
  }

  .language-selector {
      display: flex;
      align-items: center;
      gap: 5px;
      font-family: 'Gerbera-Regular', Arial, sans-serif;
      color: #333;
      font-size: 14px;
  }

  .title {
      text-align: center;
      font-family: 'Gerbera-Regular', Arial, sans-serif;
      font-size: 32px;
      font-weight: 600;
      margin-bottom: 40px;
      color: #333;
  }

  .main-content {
      display: flex;
      gap: 30px;
      align-items: flex-start;
  }

  .left-section {
      flex: 1;
      text-align: left;
  }

  .right-section {
      width: 320px;
  }

  .museum-info {
      background: transparent;
      padding: 0;
      margin-bottom: 30px;
      width: 100%;
      text-align: left;
  }

  .museum-title {
      font-family: 'Gerbera-Regular', Arial, sans-serif;
      font-size: 20px;
      font-weight: 600;
      margin-bottom: 15px;
      color: #000000;
      line-height: 1.5;
      text-align: left;
      width: 100%;
      max-width: 100%;
  }

  .museum-description {
      font-family: 'Gerbera-Regular', Arial, sans-serif;
      font-size: 16px;
      color: #666666;
      line-height: 1.6;
      margin-bottom: 20px;
      text-align: left;
      width: 100%;
  }

  .calendar-section {
      background: white;
      border: 1px solid #e0e0e0;
      border-radius: 8px;
      padding: 20px;
      margin-bottom: 30px;
  }

  .calendar-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
  }

  .calendar-nav {
      background: none;
      border: none;
      font-family: 'Gerbera-Regular', Arial, sans-serif;
      font-size: 18px;
      cursor: pointer;
      color: #666;
  }

  .calendar-grid {
      display: grid;
      grid-template-columns: repeat(7, 1fr);
      gap: 0;
      margin-bottom: 20px;
      border: 1px solid #CCCCCC;
  }

  .calendar-day-header {
      text-align: center;
      font-family: 'Gerbera-Regular', Arial, sans-serif;
      font-size: 14px;
      color: #000000;
      padding: 15px 0;
      font-weight: 500;
      border: 1px solid #CCCCCC;
      background: #F8F8F8;
      min-height: 60px;
      display: flex;
      align-items: center;
      justify-content: center;
  }

  .calendar-day {
      text-align: center;
      padding: 40px 10px;
      border: 1px solid #CCCCCC;
      cursor: pointer;
      font-family: 'Gerbera-Regular', Arial, sans-serif;
      font-size: 24px;
      font-weight: 400;
      transition: all 0.2s;
      background: white;
      color: #999999;
      min-height: 100px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      position: relative;
  }

  .calendar-day small {
      font-family: 'Gerbera-Regular', Arial, sans-serif;
      font-size: 12px;
      color: #999999;
      margin-top: 5px;
  }

  .calendar-day:hover {
      background: #f9f9f9;
  }

  .calendar-day.selected {
      border: 2px solid #D4AF37;
      background: white;
      color: #333;
  }

  .calendar-day.selected small {
      color: #333;
  }

  .time-slots {
      display: grid;
      grid-template-columns: repeat(8, 1fr);
      gap: 6px;
      margin-bottom: 20px;
  }

  .time-slot {
      padding: 10px 12px;
      border: 1px solid #000000;
      text-align: center;
      font-family: 'Gerbera-Regular', Arial, sans-serif;
      font-size: 12px;
      cursor: pointer;
      background: white;
      transition: all 0.2s;
      min-height: 36px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 500;
  }

  .time-slot:hover {
      border-color: #333;
  }

  .time-slot.selected {
      background: black;
      color: white;
      border-color: black;
  }

  .tickets-section {
      background: white;
      border: 1px solid #e0e0e0;
      border-radius: 8px;
      padding: 20px;
  }

  .tickets-header {
      display: grid;
      grid-template-columns: 2fr 1fr 1fr;
      gap: 20px;
      padding: 15px 0;
      border-bottom: 2px solid #e0e0e0;
      margin-bottom: 10px;
  }

  .header-category {
      font-size: 14px;
      font-weight: 600;
      color: #999;
      text-transform: uppercase;
  }

  .header-price {
      font-size: 14px;
      font-weight: 600;
      color: #999;
      text-transform: uppercase;
      text-align: center;
  }

  .header-quantity {
      font-size: 14px;
      font-weight: 600;
      color: #999;
      text-transform: uppercase;
      text-align: center;
  }

  .ticket-row {
      display: grid;
      grid-template-columns: 2fr 1fr 1fr;
      gap: 20px;
      align-items: center;
      padding: 20px 0;
      border-bottom: 1px solid #f0f0f0;
  }

  .ticket-row:last-child {
      border-bottom: none;
  }

  .ticket-info {
      display: flex;
      flex-direction: column;
  }

  .ticket-title {
      font-family: 'Gerbera-Regular', Arial, sans-serif;
      font-size: 16px;
      color: #333;
      margin-bottom: 2px;
      line-height: 1.3;
      display: flex;
      align-items: center;
      gap: 8px;
  }

  .pushkin-icon {
      width: 40px;
      height: 40px;
      vertical-align: middle;
      display: inline-block;
  }

  .ticket-description {
      font-family: 'Gerbera-Regular', Arial, sans-serif;
      font-size: 12px;
      color: #666;
  }

  .ticket-price {
      font-size: 18px;
      font-weight: 600;
      color: #333;
      text-align: center;
  }

  .quantity-control {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 15px;
  }

  .quantity-btn {
      width: 40px;
      height: 40px;
      border: 2px solid #CCCCCC;
      border-radius: 50%;
      background: white;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 20px;
      font-weight: 300;
      color: #666;
      transition: all 0.2s;
  }

  .quantity-btn:hover {
      border-color: #333;
      color: #333;
  }

  .quantity {
      font-size: 18px;
      font-weight: 600;
      min-width: 30px;
      text-align: center;
      color: #333;
  }

  .booking-panel {
      background: white;
      border: 1px solid #e0e0e0;
      border-radius: 8px;
      padding: 20px;
  }

  .booking-info {
      padding: 15px;
      border-radius: 6px;
      margin-bottom: 20px;
      font-size: 12px;
      color: #666;
  }

  .booking-header {
      font-size: 14px;
      color: #999;
      margin-bottom: 10px;
  }

  .museum-name {
      font-size: 14px;
      font-weight: 600;
      color: #333;
      margin-bottom: 8px;
      line-height: 1.3;
  }

  .additional-option {
      color: #4CAF50;
      font-size: 12px;
      margin: 8px 0;
  }

  .price-display {
      text-align: right;
      font-size: 16px;
      font-weight: 600;
      color: #333;
  }

  /* Weekend colors for calendar */
  .calendar-day-header.weekend {
      color: #D4AF37;
  }

  .booking-date {
      font-weight: 600;
      color: #333;
  }

  .show-all-tickets {
      background: white;
      color: #333;
      border: 2px solid #333;
      padding: 12px 24px;
      border-radius: 25px;
      cursor: pointer;
      width: 100%;
      margin-bottom: 20px;
      font-size: 14px;
      font-weight: 600;
      transition: all 0.2s;
  }

  .show-all-tickets:hover {
      background: #333;
      color: white;
  }

  .total-section {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
      font-size: 18px;
      font-weight: 600;
      color: #D4AF37;
      padding-top: 15px;
      border-top: 1px solid #e0e0e0;
  }

  .form-group {
      margin-bottom: 15px;
  }

  .form-label {
      display: block;
      font-size: 12px;
      color: #666;
      margin-bottom: 5px;
  }

  .form-input {
      width: 100%;
      padding: 10px;
      border: 1px solid #e0e0e0;
      border-radius: 4px;
      font-size: 14px;
  }

  .form-input:focus {
      outline: none;
      border-color: #333;
  }

  .checkbox-group {
      display: flex;
      align-items: flex-start;
      gap: 8px;
      margin-bottom: 20px;
  }

  .checkbox {
      margin-top: 2px;
  }

  .checkbox-label {
      font-size: 11px;
      color: #666;
      line-height: 1.3;
  }

  .pay-button {
      background: #D4AF37;
      color: white;
      border: none;
      padding: 14px 24px;
      border-radius: 6px;
      cursor: pointer;
      width: 100%;
      margin-bottom: 12px;
      font-size: 15px;
      font-weight: 500;
      transition: all 0.2s ease;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .pay-button:hover {
      background: #C19B26;
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  }

  .pay-button:active {
      background: #B8941F;
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
      transform: translateY(1px);
  }

  .payment-info {
      background: #f8f9fa;
      color: #6c757d;
      padding: 12px;
      border-radius: 6px;
      text-align: center;
      font-size: 12px;
      font-weight: 400;
      line-height: 1.4;
      border: 1px solid #e9ecef;
  }
/* Virtual Exhibition Page */
.virtual-exhibition-page {
  position: relative;
  width: 100%;
  min-height: 100vh;
  padding-top: 120px;
  background: #ffffff;
}

/* Exhibition Container */
.exhibition-container {
  position: relative;
  width: 1119px;
  margin: 0 auto;
  background: linear-gradient(135deg, #9C8F80 0%, #8A7D6F 100%);
  border-radius: 20px;
  padding: 60px 80px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
}

/* Exhibition Title */
.exhibition-title {
  font-family: 'Gerbera-Regular', Arial, sans-serif;
  font-style: normal;
  font-weight: 500;
  font-size: 48px;
  line-height: 60px;
  color: #FFFFFF;
  text-align: center;
  margin: 0 0 40px 0;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
}

/* Main Text */
.exhibition-main-text {
  font-family: 'Gerbera-Regular', Arial, sans-serif;
  font-style: normal;
  font-weight: 400;
  font-size: 18px;
  line-height: 28px;
  color: #FFFFFF;
  margin-bottom: 60px;
  text-align: justify;
}

.exhibition-main-text p {
  margin: 0 0 20px 0;
}

.exhibition-main-text p:last-child {
  margin-bottom: 0;
}

/* Exhibition Images Container */
.exhibition-images {
  display: flex;
  flex-direction: column;
  gap: 40px;
  margin-bottom: 60px;
}

/* Image Wrapper */
.exhibition-image-wrapper {
  position: relative;
  width: 100%;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.exhibition-image-wrapper:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
}

/* Exhibition Image */
.exhibition-image {
  width: 100%;
  height: auto;
  display: block;
  border-radius: 15px;
}

/* Image Caption */
.image-caption {
  font-family: 'Gerbera-Regular', Arial, sans-serif;
  font-size: 16px;
  line-height: 24px;
  color: #FFFFFF;
  text-align: center;
  padding: 20px;
  margin: 0;
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(10px);
}

/* Chronicle Section */
.chronicle-section {
  margin-top: 60px;
  padding-top: 40px;
  border-top: 2px solid rgba(255, 255, 255, 0.3);
}

/* Chronicle Title */
.chronicle-title {
  font-family: 'Gerbera-Regular', Arial, sans-serif;
  font-style: normal;
  font-weight: 500;
  font-size: 36px;
  line-height: 48px;
  color: #FFFFFF;
  text-align: center;
  margin: 0 0 40px 0;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
}

/* Chronicle Content */
.chronicle-content {
  display: flex;
  flex-direction: column;
  gap: 50px;
}

/* Chronicle Year Group */
.chronicle-year-group {
  display: flex;
  flex-direction: column;
  gap: 25px;
}

/* Chronicle Year Title */
.chronicle-year-title {
  font-family: 'Gerbera-Regular', Arial, sans-serif;
  font-style: normal;
  font-weight: 700;
  font-size: 36px;
  line-height: 48px;
  color: #FCD619;
  margin: 0 0 20px 0;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  text-align: center;
  padding-bottom: 15px;
  border-bottom: 3px solid #FCD619;
}

/* Chronicle Item */
.chronicle-item {
  display: flex;
  flex-direction: column;
  gap: 15px;
  padding: 25px 30px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  border-left: 4px solid #FCD619;
  transition: all 0.3s ease;
}

.chronicle-item:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateX(10px);
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2);
}

/* Chronicle Date */
.chronicle-date {
  font-family: 'Gerbera-Regular', Arial, sans-serif;
  font-style: normal;
  font-weight: 600;
  font-size: 20px;
  line-height: 28px;
  color: #FCD619;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

/* Chronicle Text */
.chronicle-text {
  font-family: 'Gerbera-Regular', Arial, sans-serif;
  font-style: normal;
  font-weight: 400;
  font-size: 16px;
  line-height: 26px;
  color: rgba(255, 255, 255, 0.95);
  margin: 0;
  text-align: justify;
}

/* Responsive Design */
@media screen and (max-width: 1200px) {
  .exhibition-container {
    width: 90%;
    padding: 40px 50px;
  }

  .exhibition-title {
    font-size: 36px;
    line-height: 48px;
  }

  .chronicle-title {
    font-size: 28px;
    line-height: 36px;
  }
}

@media screen and (max-width: 768px) {
  .virtual-exhibition-page {
    padding-top: 100px;
  }

  .exhibition-container {
    width: 95%;
    padding: 30px 20px;
  }

  .exhibition-title {
    font-size: 28px;
    line-height: 36px;
  }

  .exhibition-main-text {
    font-size: 16px;
    line-height: 24px;
  }

  .chronicle-year-title {
    font-size: 28px;
    line-height: 36px;
  }

  .chronicle-item {
    padding: 20px;
  }

  .chronicle-date {
    font-size: 16px;
    line-height: 24px;
  }

  .chronicle-text {
    font-size: 14px;
    line-height: 22px;
  }
}
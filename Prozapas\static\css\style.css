/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: <PERSON><PERSON>, sans-serif;
    line-height: 1.5;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

/* Header styles */
.header {
    background-color: #fff9e6;
    padding: 15px 0;
    border-bottom: 1px solid #e5e5e5;
    position: sticky;
    top: 0;
    z-index: 1000;
    transition: all 0.3s ease;
}

.header__container {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.logo {
    display: flex;
    align-items: center;
    text-decoration: none;
    color: #333;
}

.logo__img {
    width: 40px;
    height: 40px;
    margin-right: 10px;
}

.logo__text {
    font-size: 24px;
    font-weight: bold;
}

.nav__list {
    display: flex;
    list-style: none;
    gap: 30px;
}

.nav__link {
    text-decoration: none;
    color: #333;
    font-weight: 500;
    transition: color 0.3s;
    display: inline-block;
}

.nav__link:hover {
    color: #666;
    transform: translateY(-2px);
}

/* Main content */
.main {
    flex: 1;
    padding: 30px 0;
}

/* Footer styles */
.footer {
    background-color: #fff9e6;
    padding: 40px 0;
    margin-top: auto;
    transition: all 0.3s ease;
}

.footer__container {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;
}

.footer__column {
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.5s ease, transform 0.5s ease;
}

.footer__column.animate {
    opacity: 1;
    transform: translateY(0);
}

.footer__title {
    font-size: 18px;
    margin-bottom: 20px;
    color: #333;
}

.footer__list {
    list-style: none;
}

.footer__item {
    margin-bottom: 10px;
    transition: transform 0.3s ease;
}

.footer__item:hover {
    transform: translateX(5px);
}

.footer__link {
    text-decoration: none;
    color: #666;
    transition: color 0.3s;
}

.footer__link:hover {
    color: #d4a017;
}

.social {
    display: flex;
    gap: 15px;
}

.social__link {
    text-decoration: none;
    color: #666;
    transition: color 0.3s, transform 0.3s;
}

.social__link:hover {
    color: #d4a017;
    transform: scale(1.1);
}


.cart {
    text-decoration: none;
    color: #fff;
    background-color: #d4a017;
    padding: 8px 16px;
    border-radius: 4px;
    transition: background-color 0.3s;
}

.cart:hover {
    background-color: #b88a14;
}

/* Main content */
.main {
    flex: 1;
    padding: 30px 0;
}

/* Footer styles */
.footer {
    background-color: #fff9e6;
    padding: 40px 0;
    margin-top: auto;
}

.footer__container {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;
}

.footer__title {
    font-size: 18px;
    margin-bottom: 20px;
    color: #333;
}

.footer__list {
    list-style: none;
}

.footer__item {
    margin-bottom: 10px;
}

.footer__link {
    text-decoration: none;
    color: #666;
    transition: color 0.3s;
}

.footer__link:hover {
    color: #333;
}

.social {
    display: flex;
    gap: 15px;
}

.social__link {
    text-decoration: none;
    color: #666;
    transition: color 0.3s;
}

.social__link:hover {
    color: #333;
}




/* Hero section */

.hero {
    background-color: #fff9e6;
    padding: 80px 0;
    text-align: center;
}

.hero__content {
    max-width: 800px;
    margin: 0 auto;
    padding: 0 15px;
}

.hero__title {
    font-size: 48px;
    line-height: 1.2;
    margin-bottom: 40px;
    color: #333;
}

.hero__button {
    display: inline-block;
    background-color: #d4a017;
    color: #fff;
    text-decoration: none;
    padding: 16px 32px;
    border-radius: 4px;
    font-size: 18px;
    transition: background-color 0.3s;
}

.hero__button:hover {
    background-color: #b88a14;
}

/* About section */
.about {
    padding: 80px 0;
}

.about__title {
    font-size: 36px;
    color: #333;
    margin-bottom: 40px;
}

.about__content {
    max-width: 800px;
}

.about__subtitle {
    font-size: 24px;
    color: #333;
    margin-bottom: 24px;
}

.about__text {
    font-size: 18px;
    line-height: 1.6;
    color: #666;
    margin-bottom: 24px;
}

.about__text:last-child {
    margin-bottom: 0;
}


/* Popular Products section */
.popular-products {
    padding: 80px 0;
    background-color: #fff;
}

.popular-products__title {
    font-size: 36px;
    color: #333;
    margin-bottom: 40px;
}

.popular-products__grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.product-card {
    background-color: #f8f8f8;
    border-radius: 12px;
    padding: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.product-card__image-wrapper {
    width: 100%;
    margin-bottom: 20px;
    aspect-ratio: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #fff;
    border-radius: 8px;
    overflow: hidden;
}

.product-card__image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}


.product-card__price {
    font-size: 18px;
    color: #666;
    margin-bottom: 20px;
}

.product-card__button {
    background-color: #556B2F;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 12px 24px;
    font-size: 16px;
    cursor: pointer;
    transition: background-color 0.3s;
    width: 100%;
}

.product-card__button:hover {
    background-color: #445626;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.cart-container {
    display: grid;
    grid-template-columns: 1fr 300px;
    gap: 30px;
}

.cart-item {
    display: grid;
    grid-template-columns: 200px 1fr auto;
    gap: 20px;
    padding: 20px;
    background: #f0f0f0;
    border-radius: 8px;
    margin-bottom: 20px;
}

.item-image img {
    width: 100%;
    height: auto;
    border-radius: 4px;
}

.item-details h2 {
    margin: 0;
    font-size: 24px;
}

.price {
    font-size: 18px;
    margin: 10px 0;
}

.quantity-badge {
    background: #fff9e6;
    padding: 8px 16px;
    border-radius: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.edit-btn {
    background: none;
    border: none;
    cursor: pointer;
    padding: 0;
}

.cart-summary {
    background: #f0f0f0;
    padding: 20px;
    border-radius: 8px;
    height: fit-content;
}

.summary-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15px;
}

.summary-total {
    display: flex;
    justify-content: space-between;
    font-size: 24px;
    font-weight: bold;
    margin: 20px 0;
}

.cart-summary__details {
    background-color: #f8f8f8;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
}

.cart-summary__subtotal,
.cart-summary__delivery {
    font-size: 16px;
    color: #666;
    margin-bottom: 10px;
}

.cart-summary__total {
    font-size: 20px;
    font-weight: bold;
    color: #333;
    padding-top: 10px;
    border-top: 1px solid #ddd;
}


.order-btn {
    width: 100%;
    padding: 15px;
    background: #e6a756;
    border: none;
    border-radius: 4px;
    color: white;
    font-size: 18px;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
}

.order-btn:hover {
    background: #d99843;
}

.empty-cart {
    text-align: center;
    font-size: 20px;
    margin: 40px 0;
}

.continue-shopping {
    display: block;
    text-align: center;
    color: #e6a756;
    text-decoration: none;
    font-size: 18px;
}


/* Summary styles */
.summary {
    background-color: #f8f8f8;
    padding: 24px;
    border-radius: 12px;
}

.summary__row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 16px;
    font-size: 16px;
    color: #666;
}

.summary__row--total {
    margin-top: 24px;
    padding-top: 16px;
    border-top: 1px solid #e5e5e5;
    font-size: 20px;
    font-weight: bold;
    color: #333;
}

.summary__button {
    width: 100%;
    padding: 16px;
    background-color: #d4a017;
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 18px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    transition: background-color 0.3s;
}

.summary__button:hover {
    background-color: #b88a14;
}

/* Cart Container */
/* Корзина */
.cart-section {
  padding: 40px;
  background-color: #f5f5f5;
}

.cart-items {
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  gap: 20px;
}

.cart-item {
  display: flex;
  align-items: center;
  background-color: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.cart-item__image {
  width: 80px;
  height: 80px;
  object-fit: cover;
  border-radius: 4px;
  margin-right: 20px;
}

.cart-item__details {
  flex-grow: 1;
}

.cart-item__details h3 {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 8px;
}

.cart-item__details .quantity-controls {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.cart-item__details .quantity-controls button {
  background-color: #f1f1f1;
  border: none;
  padding: 4px 8px;
  font-size: 16px;
  cursor: pointer;
}

.cart-item__details .quantity-controls input {
  width: 50px;
  text-align: center;
  border: 1px solid #ccc;
  padding: 4px;
  font-size: 14px;
}

.cart-item__details .price {
  font-size: 16px;
  font-weight: 600;
}

.cart-item__details button.remove {
  background-color: #ff6b6b;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
}

.cart-summary {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  margin-top: 20px;
}

.cart-summary .total-price {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 16px;
}

.cart-summary button.checkout {
  background-color: #4CAF50;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 4px;
  font-size: 16px;
  cursor: pointer;
}

.order-form {
    margin-top: 2rem;
    padding: 2rem;
    background-color: #f8f8f8;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.order-form__title {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: #333;
}

.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: #666;
}

.form-group input[type="text"],
.form-group input[type="tel"],
.form-group input[type="email"] {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 1rem;
}

.form-group input.error {
    border-color: #dc3545;
}

.error-message {
    color: #dc3545;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

.radio-group {
    display: flex;
    align-items: center;
}

.radio-group input[type="radio"] {
    margin-right: 0.5rem;
}


/* Анимации */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideInFromLeft {
    from { transform: translateX(-50px); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

@keyframes slideInFromRight {
    from { transform: translateX(50px); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

/* Применение анимаций */
.hero {
    animation: fadeIn 1s ease-out;
}

.hero__title {
    animation: slideInFromLeft 1s ease-out;
}

.hero__button {
    animation: slideInFromRight 1s ease-out;
}

.product-card {
    transition: all 0.3s ease;
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0,0,0,0.1);
}

.nav__link {
    position: relative;
}

.nav__link::after {
    content: '';
    position: absolute;
    width: 100%;
    height: 2px;
    bottom: -5px;
    left: 0;
    background-color: #d4a017;
    visibility: hidden;
    transform: scaleX(0);
    transition: all 0.3s ease-in-out;
}

.nav__link:hover::after {
    visibility: visible;
    transform: scaleX(1);
}

.cart {
    transition: all 0.3s ease;
}

.cart:hover {
    transform: scale(1.05);
}

.footer {
    animation: fadeIn 1s ease-out;
}


/* Обновленные стили для кнопок */
.button {
    display: inline-block;
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    text-align: center;
}

.button:hover {
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.button--primary {
    background-color: #d4a017;
    color: white;
}

.button--primary:hover {
    background-color: #b88a14;
}

.button--danger {
    background-color: #dc3545;
    color: white;
}

.button--danger:hover {
    background-color: #c82333;
}

.button--warning {
    background-color: #ffc107;
    color: #000;
}

.button--warning:hover {
    background-color: #e0a800;
}

/* Обновленные стили корзины */
.cart-section {
    padding: 2rem;
}

.cart-title {
    font-size: 2rem;
    margin-bottom: 2rem;
    color: #333;
}

.cart-items {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.cart-item {
    display: flex;
    gap: 2rem;
    padding: 1.5rem;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.cart-item__image {
    width: 120px;
    height: 120px;
    object-fit: cover;
    border-radius: 4px;
}

.cart-item__details {
    flex: 1;
}

.cart-item__name {
    font-size: 1.25rem;
    margin-bottom: 0.5rem;
    color: #333;
}

.cart-item__weight {
    margin-bottom: 0.5rem;
}

.weight-input {
    width: 80px;
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    margin-left: 0.5rem;
}

.cart-item__quantity,
.cart-item__price,
.cart-item__total {
    margin-bottom: 0.5rem;
    color: #666;
}

.cart-summary {
    margin-top: 2rem;
    padding: 1.5rem;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.cart-summary__total {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: #333;
}

.cart-actions {
    display: flex;
    gap: 1rem;
}

.cart-empty {
    text-align: center;
    font-size: 1.25rem;
    color: #666;
    margin-bottom: 1rem;
}











/* Responsive styles */
@media (max-width: 768px) {
    .header__container {
        flex-direction: column;
        gap: 15px;
    }

    .nav__list {
        flex-direction: column;
        align-items: center;
        gap: 15px;
    }

    .footer__container {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .social {
        justify-content: center;
    }

    .hero {
        padding: 40px 0;
    }

    .hero__title {
        font-size: 32px;
        margin-bottom: 24px;
    }

    .hero__button {
        padding: 12px 24px;
        font-size: 16px;
    }

    .about {
        padding: 40px 0;
    }

    .about__title {
        font-size: 28px;
        margin-bottom: 24px;
    }

    .about__subtitle {
        font-size: 20px;
    }

    .about__text {
        font-size: 16px;
    }
    {

    }
    .popular-products {
        padding: 40px 0;
    }

    .popular-products__title {
        font-size: 28px;
        margin-bottom: 24px;
    }

    .product-card__title {
        font-size: 20px;
    }

    .product-card__price {
        font-size: 16px;
    }

     .cart__container {
        grid-template-columns: 1fr;
    }

    .cart-item {
        grid-template-columns: 80px 1fr;
    }

    .cart-item__image-wrapper {
        width: 80px;
        height: 80px;
    }

    .cart-item__controls {
        grid-column: 1 / -1;
        justify-content: flex-end;
    }
}
document.addEventListener('DOMContentLoaded', function() {
    // Анимация для карточек товаров
    const productCards = document.querySelectorAll('.product-card');
    productCards.forEach(card => {
        card.addEventListener('mouseenter', () => {
            card.style.transition = 'all 0.3s ease';
            card.style.transform = 'translateY(-5px)';
            card.style.boxShadow = '0 10px 20px rgba(0,0,0,0.1)';
        });
        card.addEventListener('mouseleave', () => {
            card.style.transform = 'translateY(0)';
            card.style.boxShadow = 'none';
        });
    });

    // Анимация для кнопок
    const buttons = document.querySelectorAll('button, .hero__button');
    buttons.forEach(button => {
        button.addEventListener('mouseenter', () => {
            button.style.transition = 'all 0.3s ease';
            button.style.transform = 'scale(1.05)';
        });
        button.addEventListener('mouseleave', () => {
            button.style.transform = 'scale(1)';
        });
    });

    // Плавное появление секций при скролле
    const sections = document.querySelectorAll('section');
    const observerOptions = {
        root: null,
        rootMargin: '0px',
        threshold: 0.1
    };

    const sectionObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.transition = 'opacity 1s ease-out, transform 1s ease-out';
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
                observer.unobserve(entry.target);
            }
        });
    }, observerOptions);

    sections.forEach(section => {
        section.style.opacity = '0';
        section.style.transform = 'translateY(20px)';
        sectionObserver.observe(section);
    });
});

document.addEventListener('DOMContentLoaded', function() {
    const addressInput = document.getElementById('address');

    // Автозаполнение частей адреса
    addressInput.addEventListener('blur', function(e) {
        let value = e.target.value.trim();

        // Проверка на наличие обязательных частей
        if (!value.startsWith('г.')) {
            value = 'г. ' + value;
        }
        if (!value.includes('ул.')) {
            const parts = value.split(',');
            if (parts.length > 1) {
                parts[1] = 'ул. ' + parts[1].trim();
            } else {
                value += ', ул. ';
            }
            value = parts.join(', ');
        }
        if (!value.includes('д.')) {
            const parts = value.split(',');
            if (parts.length > 2) {
                parts[2] = 'д. ' + parts[2].trim();
            } else {
                value += ', д. ';
            }
            value = parts.join(', ');
        }

        // Обновляем поле с отформатированным адресом
        e.target.value = value;
    });

    // Проверка валидации после форматирования
    addressInput.addEventListener('input', function(e) {
        const regex = /^г\.\s?[а-яА-ЯёЁ]+,\s?ул\.\s?[а-яА-ЯёЁ\s]+,\s?д\.\s?\d+$/;
        if (!regex.test(e.target.value)) {
            addressInput.setCustomValidity('Введите адрес в формате: "г. Москва, ул. Примерная, д. 1"');
        } else {
            addressInput.setCustomValidity('');
        }
    });
});


document.addEventListener('DOMContentLoaded', function() {
    const phoneInput = document.getElementById('phone');
    const addressInput = document.getElementById('address');
    const orderForm = document.getElementById('order-form');

    // Phone number mask
    phoneInput.addEventListener('input', function (e) {
        let x = e.target.value.replace(/\D/g, '').match(/(\d{0,1})(\d{0,3})(\d{0,3})(\d{0,2})(\d{0,2})/);
        e.target.value = !x[2] ? x[1] : '+7 (' + x[2] + ') ' + x[3] + (x[4] ? '-' + x[4] : '') + (x[5] ? '-' + x[5] : '');
    });

    addressInput.addEventListener('input', function(e) {
        let value = e.target.value;

    });

    // Form submission
    orderForm.addEventListener('submit', function(e) {
        e.preventDefault();

        // Perform client-side validation here if needed

        fetch('/submit_order', {
            method: 'POST',
            body: new FormData(orderForm)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Заказ успешно оформлен!');
                // Clear the cart
                fetch('/clear_cart', { method: 'POST' })
                    .then(() => {
                        window.location.href = '/shop';  // Redirect to shop page
                    });
            } else {
                alert('Ошибка при оформлении заказа: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Произошла ошибка при отправке заказа');
        });
    });
});


document.addEventListener('DOMContentLoaded', function() {
    // Функция добавления товара в корзину
    window.addToCart = function(productId) {
        fetch(`/add_to_cart/${productId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
        }).then(response => {
            if (response.ok) {
                const cartCount = document.querySelector('.cart__count');
                cartCount.textContent = Number(cartCount.textContent) + 1;
                cartCount.style.transition = 'transform 0.3s ease';
                cartCount.style.transform = 'scale(1.2)';
                setTimeout(() => {
                    cartCount.style.transform = 'scale(1)';
                }, 300);
                alert('Товар добавлен в корзину!');
            } else {
                alert('Не удалось добавить товар в корзину.');
            }
        });
    }

    // Функция удаления товара из корзины
    window.removeFromCart = function(productId) {
        fetch(`/remove_from_cart/${productId}`, {
            method: 'POST',
        }).then(response => {
            if (response.ok) {
                window.location.reload();
            } else {
                alert('Не удалось удалить товар из корзины.');
            }
        });
    }

    // Функция обновления веса товара
    window.updateItemWeight = function(productId, weight) {
        fetch('/update_cart_item', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                id: productId,
                weight: weight
            })
        }).then(response => response.json())
        .then(data => {
            if (data.success) {
                // Обновляем цены без перезагрузки страницы
                const itemElement = document.querySelector(`#item-${productId}`);
                if (itemElement) {
                    const pricePerKg = parseFloat(itemElement.dataset.price);
                    const newTotal = pricePerKg * weight;

                    // Обновляем итоговую цену товара
                    itemElement.querySelector('.cart-item__total').textContent =
                        `Итого: ${newTotal.toFixed(1)} ₽`;

                    // Обновляем общую сумму
                    updateCartTotal();
                }
            } else {
                alert('Не удалось обновить вес товара');
            }
        });
    }

    // Функция обновления общей суммы
    function updateCartTotal() {
        const itemTotals = document.querySelectorAll('.cart-item__total');
        const subtotal = Array.from(itemTotals)
            .reduce((sum, el) => {
                const price = parseFloat(el.textContent.split(' ')[1]);
                return sum + price;
            }, 0);

        const deliveryCost = 200; // Стоимость доставки
        const total = subtotal + deliveryCost;

        // Обновляем отображение сумм
        document.querySelector('.cart-summary__subtotal').textContent =
            `Сумма товаров: ${subtotal.toFixed(1)} ₽`;
        document.querySelector('.cart-summary__total').textContent =
            `Общая сумма: ${total.toFixed(1)} ₽`;
    }

        // Анимация футера
    function animateFooter() {
        const footerColumns = document.querySelectorAll('.footer__column');
        footerColumns.forEach((column, index) => {
            setTimeout(() => {
                column.classList.add('animate');
            }, index * 200); // Задержка анимации для каждой колонки
        });
    }

    // Запускаем анимацию футера, когда он появляется в поле зрения
    const footer = document.querySelector('.footer');
    const footerObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                animateFooter();
                footerObserver.unobserve(entry.target);
            }
        });
    }, { threshold: 0.1 });

    if (footer) {
        footerObserver.observe(footer);
    }
});
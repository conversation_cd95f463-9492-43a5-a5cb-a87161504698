{% extends "base.html" %}

{% block title %}Корзина{% endblock %}

{% block content %}
<section class="cart-section">
    <h1 class="cart-title">Ваша корзина</h1>

    {% if cart_items %}
        <div class="cart-items">
            {% for item in cart_items %}
            <div class="cart-item" id="item-{{ item.id }}"
                 data-price="{{ item.price }}">
                <img src="{{ item.image }}" alt="{{ item.name }}" class="cart-item__image">
                <div class="cart-item__details">
                    <h3 class="cart-item__name">{{ item.name }}</h3>
                    <div class="cart-item__weight">
                        <label for="weight-{{ item.id }}">Вес (кг):</label>
                        <input
                            type="number"
                            id="weight-{{ item.id }}"
                            value="{{ item.weight }}"
                            min="0.1"
                            step="0.1"
                            class="weight-input"
                            onchange="updateItemWeight({{ item.id }}, this.value)"
                        >
                    </div>
                    <p class="cart-item__price">Цена за кг: {{ item.price }} ₽</p>
                    <p class="cart-item__total">Итого: {{ item.total_price }} ₽</p>
                    <button onclick="removeFromCart({{ item.id }})" class="button button--danger">
                        Удалить
                    </button>
                </div>
            </div>
            {% endfor %}
        </div>

        <div class="cart-summary">
            <div class="cart-summary__details">
                <p class="cart-summary__subtotal">Сумма товаров: {{ total_price }} ₽</p>
                <p class="cart-summary__delivery">Стоимость доставки: 200 ₽</p>
                <p class="cart-summary__total">Общая сумма: {{ total_price + 200 }} ₽</p>
            </div>
           <div class="cart-actions">
                <form action="{{ url_for('clear_cart') }}" method="POST">
                    <button type="submit" class="button button--warning">Очистить корзину</button>
                </form>
            </div>
        </div>


       <div class="order-form">
            <h2 class="order-form__title">Оформление заказа</h2>
            <form id="order-form" action="{{ url_for('submit_order') }}" method="POST">
                <div class="form-group">
                    <label for="name">Имя и фамилия</label>
                    <input type="text" id="name" name="name" required>
                    <span class="error-message hidden"></span>
                </div>
                <div class="form-group">
                    <label for="phone">Контактный телефон</label>
                    <input type="tel" id="phone" name="phone" required pattern="\+7\s?[$$]{0,1}9[0-9]{2}[$$]{0,1}\s?\d{3}[-]{0,1}\d{2}[-]{0,1}\d{2}" placeholder="+7 (999) 999-99-99">
                    <span class="error-message hidden"></span>
                </div>
                <div class="form-group">
                    <label for="email">Электронная почта</label>
                    <input type="email" id="email" name="email" required>
                    <span class="error-message hidden"></span>
                </div>
                <div class="form-group">
                    <label>Способ оплаты</label>
                    <div class="radio-group">
                        <input type="radio" id="cash" name="paymentMethod" value="cash" checked>
                        <label for="cash">Наличные при получении</label>
                    </div>
                </div>
                <button type="submit" class="button button--primary">Оформить заказ</button>
            </form>
        </div>
    {% else %}
        <p class="cart-empty">Ваша корзина пуста</p>
        <a href="{{ url_for('shop') }}" class="button button--primary">Перейти в магазин</a>
    {% endif %}
</section>


{% endblock %}

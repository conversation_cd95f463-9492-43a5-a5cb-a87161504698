{% extends "base.html" %}

{% block title %}магазин{% endblock %}

{% block content %}
<section class="popular-products">
    <h1 class="popular-products__title">Продукция</h1>
    <hr>
    <br>
    <div class="popular-products__grid">
        {% for product in products %}
        <div class="product-card">
            <div class="product-card__image-wrapper">
                <img src="{{ product.image }}" alt="{{ product.name }}" class="product-card__image">
            </div>
            <h3 class="product-card__title">{{ product.name }}</h3>
            <p class="product-card__price">{{ product.price }}₽/кг</p>
            <button class="product-card__button" onclick="addToCart({{ product.id }})">В корзину</button>
        </div>
        {% endfor %}
    </div>
</section>
{% endblock %}